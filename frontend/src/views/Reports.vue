<template>
  <div class="reports">
    <div class="app-container">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">报表统计</h1>
          <div class="page-actions">
            <el-button type="primary" @click="refreshRealtimeReport">
              <el-icon><Refresh /></el-icon>
              刷新实时数据
            </el-button>
          </div>
        </div>

        <!-- 实时报告 -->
        <div class="app-card">
          <div class="card-header">
            <h3 class="card-title">实时报告</h3>
            <el-button text @click="refreshRealtimeReport">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
          
          <div v-if="realtimeReport" class="realtime-content">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="12" :md="6">
                <div class="stats-item">
                  <el-icon class="stats-icon" style="color: #409eff">
                    <OfficeBuilding />
                  </el-icon>
                  <div class="stats-content">
                    <div class="stats-value">{{ realtimeReport.total_departments }}</div>
                    <div class="stats-label">总部门数</div>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="6">
                <div class="stats-item">
                  <el-icon class="stats-icon" style="color: #67c23a">
                    <House />
                  </el-icon>
                  <div class="stats-content">
                    <div class="stats-value">{{ realtimeReport.total_dormitories }}</div>
                    <div class="stats-label">总宿舍数</div>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="6">
                <div class="stats-item">
                  <el-icon class="stats-icon" style="color: #e6a23c">
                    <User />
                  </el-icon>
                  <div class="stats-content">
                    <div class="stats-value">{{ realtimeReport.total_beds }}</div>
                    <div class="stats-label">总床位数</div>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="6">
                <div class="stats-item">
                  <el-icon class="stats-icon" style="color: #f56c6c">
                    <TrendCharts />
                  </el-icon>
                  <div class="stats-content">
                    <div class="stats-value">{{ realtimeReport.occupancy_rate }}%</div>
                    <div class="stats-label">入住率</div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
          
          <div v-else class="loading-placeholder">
            <el-skeleton :rows="2" animated />
          </div>
        </div>

        <!-- 月度报告 -->
        <div class="app-card">
          <div class="card-header">
            <h3 class="card-title">月度报告</h3>
            <div class="card-actions">
              <el-date-picker
                v-model="selectedMonth"
                type="month"
                placeholder="选择月份"
                format="YYYY年MM月"
                value-format="YYYY-MM"
                @change="fetchMonthlyReport"
              />
              <el-button
                type="success"
                :loading="monthlyLoading"
                @click="exportMonthlyReport"
              >
                <el-icon><Download /></el-icon>
                导出Excel
              </el-button>
            </div>
          </div>
          
          <div v-if="monthlyReport">
            <div class="report-summary">
              <!-- 统计信息显示区域已简化 -->

              <el-alert
                v-if="monthlyReport.is_current_month"
                title="当月数据说明"
                type="info"
                :closable="false"
                style="margin-top: 15px"
              >
                当前为本月数据，统计截止到昨天，确保数据的准确性。
              </el-alert>
            </div>

            <!-- 宿舍部门分布表格 -->
            <div class="dormitory-department-table" style="margin-top: 20px;">
              <h4>宿舍部门分布详情</h4>
              <el-table
                :data="dormitoryDepartmentData"
                border
                style="width: 100%"
                max-height="400"
              >
                <el-table-column prop="dormitory_name" label="宿舍名称" width="150" />
                <el-table-column prop="total_residents" label="总住户数" width="100" align="center" />
                <el-table-column prop="departments" label="部门分布" min-width="300">
                  <template #default="{ row }">
                    <div v-for="dept in row.departments" :key="dept.department_name" class="department-item">
                      <el-tag type="primary" size="small" style="margin-right: 8px; margin-bottom: 4px;">
                        {{ dept.department_name }}
                      </el-tag>
                      <span class="resident-count">({{ dept.resident_count }}人)</span>
                      <div class="resident-names" style="margin-left: 10px; font-size: 12px; color: #666;">
                        {{ dept.residents.join('、') }}
                      </div>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 宿舍分摊占比 -->
            <h4 style="margin-top: 30px; margin-bottom: 20px">
              <el-icon style="margin-right: 8px; color: var(--el-color-primary)">
                <House />
              </el-icon>
              宿舍分摊占比
            </h4>

            <!-- 卡片式展示 -->
            <div class="dormitory-cards">
              <div
                v-for="dorm in monthlyReport.dormitory_allocations"
                :key="dorm.dormitory_name"
                class="dormitory-card"
              >
                <div class="card-header">
                  <div class="dormitory-info">
                    <el-icon class="dormitory-icon">
                      <House />
                    </el-icon>
                    <span class="dormitory-name">{{ dorm.dormitory_name }}</span>
                  </div>
                  <div class="allocation-badge">
                    <el-progress
                      type="circle"
                      :percentage="dorm.allocation_percentage"
                      :width="60"
                      :stroke-width="6"
                      :color="getProgressColor(dorm.allocation_percentage)"
                    >
                      <template #default="{ percentage }">
                        <span class="percentage-text">{{ percentage }}%</span>
                      </template>
                    </el-progress>
                  </div>
                </div>

                <div class="card-body">
                  <div class="department-usage-title">
                    <el-icon style="margin-right: 4px">
                      <OfficeBuilding />
                    </el-icon>
                    部门分摊占比情况
                  </div>
                  <div class="department-tags">
                    <div
                      v-for="(dept, deptId) in dorm.departments"
                      :key="deptId"
                      class="department-item"
                    >
                      <div class="department-info">
                        <el-icon class="dept-icon">
                          <User />
                        </el-icon>
                        <span class="dept-name">{{ dept.department_name }}</span>
                      </div>
                      <div class="department-percentage">
                        <span
                          class="percentage-value"
                          :class="getPercentageClass(dept.percentage_in_dorm)"
                        >
                          {{ dept.percentage_in_dorm }}%
                        </span>
                        <div
                          class="percentage-bar"
                          :style="{ width: dept.percentage_in_dorm + '%' }"
                          :class="getPercentageBarClass(dept.percentage_in_dorm)"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 如果没有数据 -->
            <div v-if="!monthlyReport.dormitory_allocations || monthlyReport.dormitory_allocations.length === 0" class="empty-state">
              <el-empty description="暂无宿舍分摊数据">
                <el-icon style="font-size: 64px; color: var(--el-color-info)">
                  <House />
                </el-icon>
              </el-empty>
            </div>
          </div>
          
          <div v-else-if="!selectedMonth" class="empty-state">
            <el-empty description="请选择月份查看报告" />
          </div>
          
          <div v-else class="loading-placeholder">
            <el-skeleton :rows="4" animated />
          </div>
        </div>

        <!-- 年度汇总功能已移除 -->
      </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Download,
  House,
  OfficeBuilding,
  User,
  TrendCharts
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

import { reportApi } from '@/api/reports'

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 响应式数据
const realtimeReport = ref(null)
const monthlyReport = ref(null)
const selectedMonth = ref(dayjs().format('YYYY-MM'))
const monthlyLoading = ref(false)

// 计算宿舍部门分布数据
const dormitoryDepartmentData = computed(() => {
  if (!monthlyReport.value || !monthlyReport.value.daily_details) return []

  const dormitoryMap = new Map()

  // 遍历每日详情数据
  monthlyReport.value.daily_details.forEach(detail => {
    if (!detail.residents || detail.residents.length === 0) return

    const dormitoryName = detail.dormitory_name
    if (!dormitoryMap.has(dormitoryName)) {
      dormitoryMap.set(dormitoryName, {
        dormitory_name: dormitoryName,
        total_residents: 0,
        departments: new Map()
      })
    }

    const dormData = dormitoryMap.get(dormitoryName)

    // 按部门统计住户
    detail.residents.forEach(resident => {
      const deptName = resident.department_name || '未分配部门'

      if (!dormData.departments.has(deptName)) {
        dormData.departments.set(deptName, {
          department_name: deptName,
          resident_count: 0,
          residents: new Set()
        })
      }

      const deptData = dormData.departments.get(deptName)
      deptData.residents.add(resident.name)
    })
  })

  // 转换为数组格式并计算统计数据
  return Array.from(dormitoryMap.values()).map(dorm => {
    const departments = Array.from(dorm.departments.values()).map(dept => ({
      department_name: dept.department_name,
      resident_count: dept.residents.size,
      residents: Array.from(dept.residents)
    }))

    const totalResidents = departments.reduce((sum, dept) => sum + dept.resident_count, 0)

    return {
      dormitory_name: dorm.dormitory_name,
      total_residents: totalResidents,
      departments: departments
    }
  }).sort((a, b) => a.dormitory_name.localeCompare(b.dormitory_name))
})

// 图表配置已移除

// 方法
const refreshRealtimeReport = async () => {
  try {
    realtimeReport.value = await reportApi.getRealtimeReport()
  } catch (error) {
    console.error('获取实时报告失败:', error)
  }
}

const fetchMonthlyReport = async () => {
  if (!selectedMonth.value) return

  monthlyLoading.value = true
  try {
    const [year, month] = selectedMonth.value.split('-')
    monthlyReport.value = await reportApi.getMonthlyReport(parseInt(year), parseInt(month))
  } catch (error) {
    console.error('获取月度报告失败:', error)
  } finally {
    monthlyLoading.value = false
  }
}

// 年度汇总方法已移除

const exportMonthlyReport = async () => {
  if (!selectedMonth.value) {
    ElMessage.warning('请先选择月份')
    return
  }
  
  try {
    const [year, month] = selectedMonth.value.split('-')
    const blob = await reportApi.exportMonthlyReport(parseInt(year), parseInt(month))
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `月度报告_${selectedMonth.value}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 年度汇总导出方法已移除

// 辅助方法
const getProgressColor = (percentage) => {
  if (percentage >= 50) return '#67c23a'  // 绿色
  if (percentage >= 30) return '#e6a23c'  // 橙色
  if (percentage >= 10) return '#409eff'  // 蓝色
  return '#f56c6c'  // 红色
}

const getDepartmentTagType = (percentage) => {
  if (percentage >= 70) return 'success'
  if (percentage >= 40) return 'warning'
  if (percentage >= 20) return 'info'
  return 'danger'
}

const getPercentageClass = (percentage) => {
  if (percentage >= 70) return 'percentage-high'
  if (percentage >= 40) return 'percentage-medium'
  if (percentage >= 20) return 'percentage-low'
  return 'percentage-very-low'
}

const getPercentageBarClass = (percentage) => {
  if (percentage >= 70) return 'bar-high'
  if (percentage >= 40) return 'bar-medium'
  if (percentage >= 20) return 'bar-low'
  return 'bar-very-low'
}

// 生命周期
onMounted(() => {
  refreshRealtimeReport()
  fetchMonthlyReport()
})
</script>

<style lang="scss" scoped>
.reports {
  .app-card {
    margin-bottom: 20px;
    
    .card-header {
      .card-actions {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }
  }
  
  .realtime-content {
    .stats-item {
      display: flex;
      align-items: center;
      padding: 20px;
      background: var(--el-bg-color-page);
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      margin-bottom: 16px;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
      }

      .stats-icon {
        font-size: 32px;
        margin-right: 16px;
        opacity: 0.8;
      }

      .stats-content {
        flex: 1;

        .stats-value {
          font-size: 24px;
          font-weight: bold;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
          line-height: 1;
        }

        .stats-label {
          color: var(--el-text-color-secondary);
          font-size: 14px;
          line-height: 1;
        }
      }
    }
  }

  .dormitory-department-table {
    .department-item {
      margin-bottom: 8px;

      .resident-count {
        font-size: 12px;
        color: #666;
        margin-left: 4px;
      }

      .resident-names {
        margin-top: 4px;
        padding-left: 8px;
        border-left: 2px solid #e4e7ed;
        line-height: 1.4;
      }
    }
  }
  
  .report-summary {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  
  .yearly-chart {
    margin-bottom: 20px;
  }
  
  .loading-placeholder,
  .empty-state {
    padding: 40px;
    text-align: center;
  }

  .department-usage {
    line-height: 1.8;

    .el-tag {
      margin-right: 8px;
      margin-bottom: 4px;
    }
  }

  h4 {
    color: var(--el-text-color-primary);
    font-weight: 600;
    border-left: 4px solid var(--el-color-primary);
    padding-left: 12px;
    display: flex;
    align-items: center;
  }

  // 宿舍分摊占比卡片样式
  .dormitory-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
  }

  .dormitory-card {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-success));
    }

    &:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      transform: translateY(-4px);
      border-color: var(--el-color-primary-light-7);
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .dormitory-info {
    display: flex;
    align-items: center;

    .dormitory-icon {
      font-size: 20px;
      color: var(--el-color-primary);
      margin-right: 8px;
    }

    .dormitory-name {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .allocation-badge {
    .percentage-text {
      font-size: 12px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .card-body {
    .department-usage-title {
      display: flex;
      align-items: center;
      font-size: 15px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid var(--el-border-color-lighter);

      .el-icon {
        color: var(--el-color-warning);
      }
    }

    .department-tags {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .department-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: var(--el-bg-color-page);
      border-radius: 8px;
      border: 1px solid var(--el-border-color-lighter);
      transition: all 0.3s ease;

      &:hover {
        background: var(--el-color-primary-light-9);
        border-color: var(--el-color-primary-light-7);
        transform: translateX(4px);
      }
    }

    .department-info {
      display: flex;
      align-items: center;

      .dept-icon {
        font-size: 16px;
        color: var(--el-color-primary);
        margin-right: 8px;
      }

      .dept-name {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }

    .department-percentage {
      display: flex;
      align-items: center;
      gap: 12px;
      min-width: 120px;

      .percentage-value {
        font-size: 18px;
        font-weight: 700;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        min-width: 50px;
        text-align: right;

        &.percentage-high {
          color: #67c23a;
          font-size: 20px;
        }

        &.percentage-medium {
          color: #e6a23c;
          font-size: 19px;
        }

        &.percentage-low {
          color: #409eff;
          font-size: 18px;
        }

        &.percentage-very-low {
          color: #f56c6c;
          font-size: 17px;
        }
      }

      .percentage-bar {
        height: 6px;
        border-radius: 3px;
        transition: all 0.3s ease;
        min-width: 60px;
        max-width: 60px;

        &.bar-high {
          background: linear-gradient(90deg, #67c23a, #85ce61);
          box-shadow: 0 2px 4px rgba(103, 194, 58, 0.3);
        }

        &.bar-medium {
          background: linear-gradient(90deg, #e6a23c, #ebb563);
          box-shadow: 0 2px 4px rgba(230, 162, 60, 0.3);
        }

        &.bar-low {
          background: linear-gradient(90deg, #409eff, #66b1ff);
          box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
        }

        &.bar-very-low {
          background: linear-gradient(90deg, #f56c6c, #f78989);
          box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--el-text-color-secondary);

    .el-empty {
      padding: 20px;
    }
  }
}
</style>
