"""
分摊计算核心服务
"""
from typing import List, Dict, Optional
from datetime import date, timedelta
from collections import defaultdict
from dataclasses import dataclass

from app.models.record import ResidenceRecord
from app.models.dormitory import Dormitory
from app.schemas.report import DepartmentSummary, DailyAllocationDetail


@dataclass
class DepartmentAllocation:
    """部门分摊信息"""
    department_id: str
    department_name: str
    bed_count: int
    allocation_ratio: float


@dataclass
class ResidentInfo:
    """住户信息"""
    id: str
    name: str
    department_name: str
    bed_number: int


@dataclass
class DailyAllocationResult:
    """日度分摊结果"""
    date: date
    dormitory_id: str
    dormitory_name: str
    total_beds: int
    occupied_beds: int
    residents: List[ResidentInfo]
    department_allocations: List[DepartmentAllocation]


class AllocationCalculator:
    """费用分摊计算器"""
    
    def __init__(self, unit_cost_per_bed_day: float = 100.0):
        """
        初始化计算器
        
        Args:
            unit_cost_per_bed_day: 每床每日费用单价
        """
        self.unit_cost = unit_cost_per_bed_day
    
    def calculate_daily_allocation(
        self, 
        target_date: date, 
        dormitory: Dormitory,
        residence_records: List[ResidenceRecord]
    ) -> DailyAllocationResult:
        """
        计算指定日期指定宿舍的分摊结果
        
        Args:
            target_date: 目标计算日期
            dormitory: 宿舍信息
            residence_records: 入住记录列表
            
        Returns:
            DailyAllocationResult: 日度分摊结果
        """
        # 1. 筛选有效的入住记录
        active_records = self._filter_active_records(target_date, dormitory.id, residence_records)

        # 2. 提取住户信息
        residents = self._extract_resident_info(active_records)

        # 3. 按部门分组统计
        department_stats = self._group_by_department(active_records)

        # 4. 计算分摊比例
        allocations = self._calculate_allocations(department_stats, dormitory)

        return DailyAllocationResult(
            date=target_date,
            dormitory_id=dormitory.id,
            dormitory_name=dormitory.name,
            total_beds=dormitory.total_beds,
            occupied_beds=len(active_records),
            residents=residents,
            department_allocations=allocations
        )
    
    def calculate_monthly_summary(
        self,
        year: int,
        month: int,
        daily_allocations: List[DailyAllocationResult],
        end_date: Optional[date] = None
    ) -> List[DepartmentSummary]:
        """
        计算月度部门费用汇总

        Args:
            year: 年份
            month: 月份
            daily_allocations: 日度分摊结果列表
            end_date: 截止日期

        Returns:
            List[DepartmentSummary]: 部门费用汇总列表
        """
        # 按日期分组计算每日各部门的总分摊比例
        daily_department_ratios = defaultdict(lambda: defaultdict(float))  # {date: {dept_id: total_ratio}}
        department_names = {}
        daily_total_beds = defaultdict(int)  # {date: total_beds}

        # 收集每日数据
        for daily_allocation in daily_allocations:
            allocation_date = daily_allocation.date
            daily_total_beds[allocation_date] += daily_allocation.total_beds

            for dept_alloc in daily_allocation.department_allocations:
                # 该部门在该日该宿舍的床位数 = 宿舍总床位数 × 分摊比例
                dept_beds = daily_allocation.total_beds * dept_alloc.allocation_ratio
                daily_department_ratios[allocation_date][dept_alloc.department_id] += dept_beds
                department_names[dept_alloc.department_id] = dept_alloc.department_name

        # 计算每日各部门的分摊比例（相对于当日总床位数）
        daily_dept_ratios_normalized = defaultdict(lambda: defaultdict(float))  # {date: {dept_id: ratio}}
        for allocation_date, dept_beds in daily_department_ratios.items():
            total_beds_that_day = daily_total_beds[allocation_date]
            if total_beds_that_day > 0:
                for dept_id, beds in dept_beds.items():
                    daily_dept_ratios_normalized[allocation_date][dept_id] = beds / total_beds_that_day

        # 计算统计天数
        total_days = len(daily_department_ratios)
        if total_days == 0:
            return []

        # 计算各部门的月度平均分摊比例和床位天数
        summaries = []
        total_monthly_bed_days = 0

        for dept_id in department_names.keys():
            # 计算该部门的月度平均分摊比例 = Σ每日该部门分摊比例 / 统计天数
            daily_ratios_sum = sum(
                daily_dept_ratios_normalized[date].get(dept_id, 0.0)
                for date in daily_department_ratios.keys()
            )
            monthly_avg_ratio = daily_ratios_sum / total_days

            # 计算月度床位天数 = 月度平均分摊比例 × 平均每日总床位数 × 统计天数
            avg_daily_total_beds = sum(daily_total_beds.values()) / len(daily_total_beds)
            monthly_bed_days = monthly_avg_ratio * avg_daily_total_beds * total_days
            total_monthly_bed_days += monthly_bed_days

            # 计算费用
            cost = monthly_bed_days * self.unit_cost

            summaries.append(DepartmentSummary(
                department_id=dept_id,
                department_name=department_names[dept_id],
                bed_days=round(monthly_bed_days, 2),
                cost=round(cost, 2),
                ratio=round(monthly_avg_ratio, 4)
            ))

        # 按床位天数降序排序
        summaries.sort(key=lambda x: x.bed_days, reverse=True)
        return summaries

    def _filter_active_records(
        self,
        target_date: date,
        dormitory_id: str,
        records: List[ResidenceRecord]
    ) -> List[ResidenceRecord]:
        """筛选指定日期在指定宿舍的有效入住记录"""
        active_records = []
        for record in records:
            if (record.dormitory_id == dormitory_id and
                record.check_in_date <= target_date and
                (record.check_out_date is None or record.check_out_date >= target_date)):
                active_records.append(record)
        return active_records

    def _extract_resident_info(self, records: List[ResidenceRecord]) -> List[ResidentInfo]:
        """提取住户信息"""
        residents = []
        for record in records:
            if record.resident:
                residents.append(ResidentInfo(
                    id=record.resident.id,
                    name=record.resident.name,
                    department_name=record.resident.department.name if record.resident.department else "未分配部门",
                    bed_number=record.bed_number
                ))
        return residents

    def _group_by_department(self, records: List[ResidenceRecord]) -> Dict[str, Dict[str, int]]:
        """按部门分组统计住户数量"""
        department_stats = defaultdict(lambda: {"count": 0, "name": ""})

        for record in records:
            dept_id = record.resident.department_id
            dept_name = record.resident.department.name
            department_stats[dept_id]["count"] += 1
            department_stats[dept_id]["name"] = dept_name

        return dict(department_stats)

    def _calculate_allocations(
        self,
        department_stats: Dict[str, Dict[str, int]],
        dormitory
    ) -> List[DepartmentAllocation]:
        """计算各部门分摊比例"""
        allocations = []

        if not department_stats:
            # 空宿舍场景 - 宿舍所属部门承担100%
            if dormitory.department_id and dormitory.department:
                # 使用宿舍所属部门
                allocations.append(DepartmentAllocation(
                    department_id=dormitory.department_id,
                    department_name=dormitory.department.name,
                    bed_count=dormitory.total_beds,
                    allocation_ratio=1.0
                ))
            else:
                # 如果宿舍没有指定部门，则由公司承担
                allocations.append(DepartmentAllocation(
                    department_id="company",
                    department_name="公司",
                    bed_count=dormitory.total_beds,
                    allocation_ratio=1.0
                ))
        else:
            # 有人入住场景 - 按各部门人数比例分摊
            total_people = sum(stats["count"] for stats in department_stats.values())

            for dept_id, stats in department_stats.items():
                # 计算该部门的分摊比例 = 该部门人数 / 总人数
                allocation_ratio = stats["count"] / total_people
                allocations.append(DepartmentAllocation(
                    department_id=dept_id,
                    department_name=stats["name"],
                    bed_count=stats["count"],
                    allocation_ratio=allocation_ratio
                ))

        return allocations

    def get_date_range(self, start_date: date, end_date: date) -> List[date]:
        """生成日期范围"""
        dates = []
        current_date = start_date
        while current_date <= end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        return dates

    def get_month_date_range(
        self,
        year: int,
        month: int,
        end_date: Optional[date] = None
    ) -> List[date]:
        """获取月份日期范围"""
        # 月份开始日期
        month_start = date(year, month, 1)

        # 月份自然结束日期
        if month == 12:
            next_month = date(year + 1, 1, 1)
        else:
            next_month = date(year, month + 1, 1)
        month_end = next_month - timedelta(days=1)

        # 确定实际结束日期
        actual_end = min(end_date, month_end) if end_date else month_end

        return self.get_date_range(month_start, actual_end)
